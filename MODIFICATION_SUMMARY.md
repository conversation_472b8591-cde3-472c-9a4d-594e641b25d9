# 表单校验修改总结

## 修改内容

本次修改为 InvoiceOrderSelector 组件添加了对每个下拉框的单独校验功能。

### 1. 修改的文件

#### `src/application/invoice-management/components/InvoiceOrderSelector/index.vue`
- 添加了 `m-form` 包装器来支持表单校验
- 为每个下拉框添加了 `m-form-item` 包装，设置独立的校验规则
- 每个下拉框都有必填校验：`{ required: true, message: '请选择开票订单' }`

#### `src/application/invoice-management/components/InvoiceOrderSelector/index.ts`
- 导入了 `FormInstance` 类型和 `ref` 函数
- 添加了 `formRef` 表单引用
- 添加了 `formRules` 表单校验规则
- 在删除订单项时添加了清除校验错误的逻辑
- 新增了 `validateAll()` 方法用于校验所有下拉框
- 新增了 `clearValidate()` 方法用于清除校验错误
- 将新方法暴露给父组件

#### `src/application/invoice-management/components/InvoiceOrderSelector/index.less`
- 添加了 `.order-select-form-item` 样式，设置 `margin-bottom: 0`
- 调整了错误提示的样式

#### `src/application/invoice-management/components/Invoice-application-dialog/index.ts`
- 移除了对整个 `orderSelectList` 的校验规则
- 修改了 `onOrderSelectChange` 方法，移除了表单校验相关代码
- 在 `onSubmit` 方法中添加了对子组件校验方法的调用
- 添加了检查是否至少选择一个订单的逻辑

#### `src/application/invoice-management/components/Invoice-application-dialog/index.vue`
- 将订单选择区域从 `m-form-item` 改为普通的 `div` 包装
- 移除了隐藏的输入字段
- 添加了自定义的标签样式

#### `src/application/invoice-management/components/Invoice-application-dialog/index.less`
- 添加了 `.order-select-section` 样式
- 添加了 `.order-select-label` 样式，使其与表单项标签保持一致

## 功能改进

### 之前的校验方式
- 只对整个订单选择列表进行校验
- 只检查是否至少选择了一个订单
- 校验错误显示在整个组件级别

### 现在的校验方式
- 对每个下拉框进行单独校验
- 每个下拉框都有独立的必填校验
- 校验错误显示在具体的下拉框下方
- 提供了更精确的用户反馈

## 校验规则

每个下拉框都有以下校验规则：
```javascript
{ required: true, message: '请选择开票订单' }
```

## 暴露的方法

子组件现在暴露以下方法给父组件：
- `validateAll()`: 校验所有下拉框，返回 Promise<boolean>
- `clearValidate()`: 清除所有校验错误
- `getSelectedOrders()`: 获取选中的订单列表（原有）
- `reset()`: 重置组件状态（原有）
- `setOrderSelectList()`: 设置订单选择列表（原有）

## 用户体验改进

1. **更精确的错误提示**: 用户可以清楚地知道哪个具体的下拉框需要选择
2. **实时校验**: 每个下拉框都会在用户操作时进行实时校验
3. **视觉反馈**: 错误状态会直接显示在对应的下拉框下方
4. **保持一致性**: 校验行为与其他表单项保持一致

## 技术实现

使用了 Ant Design Vue 的表单校验机制：
- 利用 `m-form` 和 `m-form-item` 的嵌套结构
- 使用动态的 `name` 属性：`['orderSelectList', index, 'selectedOrderNo']`
- 通过 `FormInstance` 的 `validate()` 和 `clearValidate()` 方法进行校验控制
