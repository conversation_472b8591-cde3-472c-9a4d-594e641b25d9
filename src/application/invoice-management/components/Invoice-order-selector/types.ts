/**
 * 开票订单选择器组件类型定义
 */

/**
 * 订单选择项接口
 */
export interface OrderSelectItem {
	/** 唯一标识 */
	id: string;
	/** 选中的订单号 */
	selectedOrderNo?: string;
	/** 开票金额 */
	invoiceAmount: number;
}

/**
 * 历史订单响应接口
 */
export interface HistoryOrderResponse {
	/** 账户明细Id */
	id: number;
	/** 订单号 */
	orderNo: string;
	/** 消费时间 */
	actionTime: string;
	/** 商品名称 */
	goodsName: string;
	/** 备注 */
	comment: string;
	/** 订单金额 */
	orderAmount: number;
	/** 可开票金额 */
	invoiceableAmount: number;
	/** 能否申请开蓝票 */
	enableApplyBlueInvoice: boolean;
	/** 创建时间 */
	createTime: number;
	/** 支付时间 */
	paidTime: number;
	/** 格式化名称（用于显示） */
	formatName?: string;
}

/**
 * 组件状态接口
 */
export interface InvoiceOrderSelectorState {
	/** 订单选择列表 */
	orderSelectList: OrderSelectItem[];
}

/**
 * 组件 Props 接口
 */
export interface InvoiceOrderSelectorProps {
	/** 历史订单列表 */
	historyOrderList: HistoryOrderResponse[];
	/** 是否为老订单模式 */
	isOldOrder: boolean;
	/** 表单验证规则 */
	rules?: any;
}

/**
 * 组件 Emits 接口
 */
export interface InvoiceOrderSelectorEmits {
	/** 订单选择变化事件 */
	(event: 'change', value: OrderSelectItem[]): void;
	/** 订单金额变化事件 */
	(event: 'amount-change', totalAmount: number): void;
}
