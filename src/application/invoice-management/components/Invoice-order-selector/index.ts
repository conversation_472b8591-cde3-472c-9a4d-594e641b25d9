import { computed, defineComponent, reactive, ref, toRefs, watch, type PropType } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { type FormInstance } from 'ant-design-vue';
import { timeFilter, generateUniqueId } from '@/utils/utils';
import type {
	HistoryOrderResponse,
	InvoiceOrderSelectorState,
	OrderSelectItem
} from './types';

export default defineComponent({
	name: 'InvoiceOrderSelector',
	components: {
		PlusOutlined
	},
	props: {
		/** 历史订单列表 */
		historyOrderList: {
			type: Array as PropType<HistoryOrderResponse[]>,
			default: () => []
		},
		/** 是否为老订单模式 */
		isOldOrder: {
			type: Boolean,
			default: false
		},
		/** 表单验证规则 */
		rules: {
			type: Object,
			default: () => ({})
		}
	},
	emits: ['change', 'amount-change'],
	setup(props, { emit }) {
		// 表单引用
		const formRef = ref<FormInstance>();

		const state = reactive<InvoiceOrderSelectorState>({
			orderSelectList: [
				{
					id: generateUniqueId(),
					selectedOrderNo: undefined,
					invoiceAmount: 0
				}
			]
		});

		// 计算属性
		const computeds = {
			/** 表单数据模型 */
			formModel: computed(() => ({
				orderSelectList: state.orderSelectList
			})),
			/** 格式化的历史订单列表 */
			formatHistoryOrderList: computed(() => {
				return props.historyOrderList
					.filter(item => item.enableApplyBlueInvoice)
					.map(item => ({
						...item,
						formatName: `${timeFilter(item.paidTime, 'YYYY年MM月DD日')}购买${item.goodsName}${
							item.orderAmount
						}元`
					}));
			}),



			/** 总开票金额 */
			totalInvoiceAmount: computed(() => {
				return state.orderSelectList.reduce((total, item) => total + item.invoiceAmount, 0);
			})
		};

		// 方法
		const methods = {
			/**
			 * 添加订单选择项
			 */
			addOrderSelect() {
				const newItem: OrderSelectItem = {
					id: generateUniqueId(),
					selectedOrderNo: undefined,
					invoiceAmount: 0
				};
				state.orderSelectList.push(newItem);
				methods.emitChange();
			},

			/**
			 * 删除订单选择项
			 * @param index 索引
			 */
			removeOrderSelect(index: number) {
				if (state.orderSelectList.length > 1) {
					state.orderSelectList.splice(index, 1);
					methods.emitChange();
					methods.emitAmountChange();
					// 清除被删除项的校验错误
					setTimeout(() => {
						formRef.value?.clearValidate();
					}, 0);
				}
			},

			/**
			 * 订单选择变化处理
			 * @param value 选中的订单号
			 * @param option 选中的订单选项
			 * @param index 索引
			 */
			onChangeOrder(value: string, option: HistoryOrderResponse, index: number) {
				const item = state.orderSelectList[index];
				if (item) {
					item.selectedOrderNo = value;
					item.invoiceAmount = option?.invoiceableAmount || 0;
				}

				// 立即触发变化事件，确保父组件能及时更新验证状态
				methods.emitChange();
				methods.emitAmountChange();
			},

			/**
			 * 发送变化事件
			 */
			emitChange() {
				emit('change', [...state.orderSelectList]);
			},

			/**
			 * 发送金额变化事件
			 */
			emitAmountChange() {
				emit('amount-change', computeds.totalInvoiceAmount.value);
			},

			/**
			 * 获取选中的订单列表
			 */
			getSelectedOrders(): OrderSelectItem[] {
				return state.orderSelectList.filter(item => item.selectedOrderNo);
			},

			/**
			 * 重置组件状态
			 */
			reset() {
				state.orderSelectList = [
					{
						id: generateUniqueId(),
						selectedOrderNo: undefined,
						invoiceAmount: 0
					}
				];
				methods.emitChange();
				methods.emitAmountChange();
			},

			/**
			 * 设置订单选择列表
			 * @param orders 订单列表
			 */
			setOrderSelectList(orders: OrderSelectItem[]) {
				state.orderSelectList = orders.length > 0 ? orders : [
					{
						id: generateUniqueId(),
						selectedOrderNo: undefined,
						invoiceAmount: 0
					}
				];
				methods.emitChange();
				methods.emitAmountChange();
			},

			/**
			 * 校验所有下拉框
			 * @returns Promise<boolean> 校验是否通过
			 */
			async validateAll(): Promise<boolean> {
				console.log('订单选择器校验开始, orderSelectList:', state.orderSelectList);

				// 检查每个订单选择项是否已选择
				const unselectedIndexes: number[] = [];

				state.orderSelectList.forEach((item, index) => {
					if (!item.selectedOrderNo) {
						unselectedIndexes.push(index);
					}
				});

				console.log('未选择的订单索引:', unselectedIndexes);

				// 无论是否有错误，都触发表单校验以显示错误状态
				try {
					await formRef.value?.validate();
					console.log('订单选择器表单校验通过');
					return unselectedIndexes.length === 0;
				} catch (error) {
					console.log('订单选择器表单校验失败:', error);
					// 校验失败时返回 false，但错误信息已经显示在UI上
					return false;
				}
			},

			/**
			 * 清除所有校验错误
			 */
			clearValidate() {
				formRef.value?.clearValidate();
			}
		};

		// 监听订单选择列表变化
		watch(
			() => state.orderSelectList,
			() => {
				methods.emitAmountChange();
			},
			{ deep: true }
		);

		return {
			formRef,
			...toRefs(state),
			...computeds,
			...methods
		};
	}
});
