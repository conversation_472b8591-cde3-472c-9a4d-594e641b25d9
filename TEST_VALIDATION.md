# 表单校验测试指南

## 问题分析

点击提交时没有触发订单选择的表单校验，可能的原因：

1. **表单模型结构不匹配**：`name` 属性与 `model` 数据结构不一致
2. **校验方法调用时机**：校验方法可能没有正确触发
3. **Ant Design Vue 表单校验机制**：动态表单项的校验需要特殊处理

## 修复方案

### 1. 修复表单模型结构
```typescript
// 添加 formModel 计算属性确保数据结构匹配
formModel: computed(() => ({
    orderSelectList: state.orderSelectList
}))
```

### 2. 改进校验方法
```typescript
async validateAll(): Promise<boolean> {
    // 检查每个订单选择项是否已选择
    const unselectedIndexes: number[] = [];
    
    state.orderSelectList.forEach((item, index) => {
        if (!item.selectedOrderNo) {
            unselectedIndexes.push(index);
        }
    });
    
    if (unselectedIndexes.length > 0) {
        // 手动触发表单校验来显示错误信息
        try {
            await formRef.value?.validate();
        } catch (error) {
            // 校验失败是预期的，我们只是想显示错误信息
        }
        return false;
    }
    
    return true;
}
```

## 测试步骤

### 测试场景 1：空订单校验
1. 打开开票申请对话框
2. 不选择任何订单
3. 点击提交按钮
4. **期望结果**：每个空的下拉框下方显示"请选择开票订单"错误提示

### 测试场景 2：部分订单校验
1. 添加多个订单选择项（点击"添加"按钮）
2. 只选择部分订单，留空其他订单
3. 点击提交按钮
4. **期望结果**：只有未选择的下拉框显示错误提示

### 测试场景 3：删除订单项
1. 添加多个订单选择项
2. 不选择订单，点击提交（触发校验错误）
3. 删除其中一个订单项
4. **期望结果**：被删除项的校验错误应该消失

### 测试场景 4：正常提交
1. 选择所有订单
2. 填写其他必填字段
3. 点击提交按钮
4. **期望结果**：正常提交，无校验错误

## 调试方法

### 1. 检查表单数据结构
在浏览器控制台中检查：
```javascript
// 检查表单模型数据
console.log('formModel:', this.formModel);

// 检查订单选择列表
console.log('orderSelectList:', this.orderSelectList);
```

### 2. 检查校验方法调用
在 `validateAll` 方法中添加调试日志：
```typescript
async validateAll(): Promise<boolean> {
    console.log('validateAll called, orderSelectList:', state.orderSelectList);
    // ... 其他代码
}
```

### 3. 检查表单引用
确保 `formRef` 正确引用到表单实例：
```typescript
console.log('formRef:', formRef.value);
```

## 可能的问题和解决方案

### 问题 1：表单校验不触发
**原因**：`name` 属性格式不正确或表单模型结构不匹配
**解决方案**：确保使用正确的数组索引格式 `['orderSelectList', index, 'selectedOrderNo']`

### 问题 2：校验错误不显示
**原因**：校验规则没有正确应用到表单项
**解决方案**：使用内联规则 `:rules="[{ required: true, message: '请选择开票订单' }]"`

### 问题 3：动态表单项校验失效
**原因**：Ant Design Vue 对动态表单项的校验支持有限
**解决方案**：使用手动校验逻辑结合表单校验显示

## 备用方案

如果 Ant Design Vue 的表单校验仍然有问题，可以考虑：

1. **完全手动校验**：不依赖 Ant Design Vue 的校验机制，手动显示错误状态
2. **使用 ref 直接操作**：通过 ref 直接操作每个表单项的校验状态
3. **重构为单个表单项**：将整个订单选择器作为一个自定义表单控件

## 验证修复效果

修复后，应该能看到：
- ✅ 点击提交时，未选择的下拉框显示红色边框和错误提示
- ✅ 错误提示文字为"请选择开票订单"
- ✅ 选择订单后，对应的错误提示消失
- ✅ 删除订单项时，相关的校验错误被清除
- ✅ 所有订单都选择后，可以正常提交
